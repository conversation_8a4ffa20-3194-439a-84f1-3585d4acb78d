#addin "Cake.FileHelpers&version=4.0.1"

#addin "nuget:?package=Newtonsoft.Json&version=11.0.2"
#addin "nuget:?package=AWSSDK.Core&version=3.3.24.3"
#addin "nuget:?package=AWSSDK.S3&version=3.3.18.6"
#addin "nuget:http://proget/nuget/iss?package=Homenet.MoreCommon"

using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Homenet.MoreCommon;
using Homenet.MoreCommon.Amazon;
using Homenet.MoreCommon.SettingsHelpers;
using Amazon.Runtime;
using Amazon.S3;
using Amazon.S3.Model;
using Amazon.S3.Transfer;
using Amazon.RegionEndpoint;


// Constants
const string Region = "us-east-1";
const string PackageBucketId = "ais.authoring.legacy-deployer.packages";
const string SolutionPath = "../../";
const string ArtifactPath = "../../artifacts/";
const string ArtifactPackageFolderName = "legacy-deployer";

// Arguments
string target = Argument("target", "Default");
string branchName = Argument("branchName", string.Empty);
string account = Argument("account", "awsaaianp");
string profileName = Argument("profileName", $"{account}:PowerUser");

// Package Definition (directory name => output zip file name)
var deployablePackages = new Dictionary<string,string>
{
	["common_libs"] = "legacy-common-libraries.zip",
	["NewSystem"] = "legacy-authoring.zip",
	["ResidualsEntry"] = "legacy-residuals-entry.zip",
	["Auditing"] = "legacy-auditing.zip",
	["DigIRS"] = "legacy-digirs.zip",
	["AdvertisedPrograms"] = "legacy-advertised-programs.zip",
	["AdvPgmDelivery"] = "legacy-advertised-program-delivery.zip",
};

// Declarations
SessionAWSCredentials sessionCredentials;


/// 
/// Cleans the artifact output directory for legacy packaging
/// 
Task("Clean")
	.Does(()=>
	{
		Console.WriteLine("Cleaning artifact output directory");
		
		var artifactDirectory = Directory(ArtifactPath);
		var artifactPackageDirectory = Directory(ArtifactPath + ArtifactPackageFolderName);
		
		// Ensure the artifact directory structure needed exists
		if (!DirectoryExists(artifactDirectory))
		{
			CreateDirectory(artifactDirectory);
			CreateDirectory(artifactPackageDirectory);
		}
		else
		{
			// Clear any previously generated packages still remaining
			if (DirectoryExists(artifactPackageDirectory))
			{
				DeleteDirectory(artifactPackageDirectory, new DeleteDirectorySettings {
					Recursive = true,
					Force = true
				});
			}
			
			CreateDirectory(artifactPackageDirectory);
		}
	});

/// 
/// Verify safety checks againsts the arguments
/// 
Task("SafetyChecks")
	.Does(()=>
	{
		Console.WriteLine("Verifying safety checks");
		
		if (branchName != string.Empty && !Regex.IsMatch(branchName, @"^[a-zA-Z0-9_-]+$"))
			throw new Exception($"The provided branchName argument ({branchName}) contains invalid characters");
	});

/// 
/// Grab session credentials from ALKS 
/// 
Task("AwsCredentials")
	.Does(()=>
	{
		Console.WriteLine("Getting AWS Credentials from ALKS");
		
		// Instantize service to get AWS credentials from ALKS 
		var hnEnvironment = Homenet.MoreCommon.Settings.GetEnvironment("development");
		var alks = new AirLiftKeyServices(hnEnvironment.AlksAutoUpdate.ADUsername, hnEnvironment.AlksAutoUpdate.ADPassword.Value, "awsaaia*:PowerUser", profileName);
		
		// Create the AWS session
		sessionCredentials = new SessionAWSCredentials(alks.DefaultProfile.Key.AccessKey, alks.DefaultProfile.Key.SecretKey, alks.DefaultProfile.Key.SessionToken);   
	});

/// 
/// Packages the legacy applications for consumption by Legacy Deployer tool in Authoring 2.0
/// 
Task("Package")
	.IsDependentOn("SafetyChecks")
	.IsDependentOn("Clean")
	.IsDependentOn("AwsCredentials")
	.Does(()=>
	{
		Console.WriteLine("Beginning packaging of the application");
		
		using (var s3Client = new AmazonS3Client(sessionCredentials, Amazon.RegionEndpoint.GetBySystemName(Region)))
		{
			foreach (var package in deployablePackages)
			{
				string applicationFolderName = package.Key;
				string applicationFolderPath = MakeAbsolute(Directory(SolutionPath) + Directory(applicationFolderName)).FullPath;
				string outputZipFileName = package.Value;
				string artifactFolderPath = ArtifactPath + ArtifactPackageFolderName;
				string outputZipFilePath = MakeAbsolute(Directory(artifactFolderPath) + File(outputZipFileName)).FullPath;
				string bucketObjectName = string.IsNullOrEmpty(branchName) ? outputZipFileName : $"{branchName}/{outputZipFileName}";
				string bucketBackupObjectName = string.IsNullOrEmpty(branchName) ? $"backup/{outputZipFileName}" : $"{branchName}/backup/{outputZipFileName}";
				
				// Zip up the application
				Console.WriteLine($"Zipping application {applicationFolderName} into {outputZipFileName}");
				Zip(applicationFolderPath, outputZipFilePath);
				
				// Backup the last generated package if it exists
				Console.WriteLine($"Backing up last generated package to {bucketBackupObjectName}");
				try 
				{
					s3Client.CopyObject(new CopyObjectRequest
					{
						SourceBucket = PackageBucketId,
						SourceKey = bucketObjectName,
						DestinationBucket = PackageBucketId,
						DestinationKey = bucketBackupObjectName,
						CannedACL = S3CannedACL.Private
					});
				}
				catch (Amazon.S3.AmazonS3Exception) {} // No previous backup so ignore
				
				// Upload the new package
				Console.WriteLine($"Uploading the new generated package to {bucketObjectName}");
				s3Client.PutObject(new PutObjectRequest
				{
					BucketName = PackageBucketId,
					Key = bucketObjectName,
					FilePath = outputZipFilePath,
					CannedACL = S3CannedACL.Private
				});
			}
		}
	});

///
/// Default task
///
Task("Default")
	.IsDependentOn("Package")
	.Does(() => {});


// Run the target
RunTarget(target);