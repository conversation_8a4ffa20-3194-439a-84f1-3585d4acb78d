# Deploy Docker Images to AWS ECR Repository
# 
# Dependencies:
# Docker, AWS CLI, AWS Powershell Tools

param (
	# Name of repository name, as well as the image name
	[Parameter(Mandatory=$true)]
	[string]$RepositoryName,
	
	# Optionally override the path to find the Dockerfile which defaults to $RepositoryName as 
	# subfolder for location where DockerFiles are kept in solution.
	[Parameter(Mandatory=$false)]
	[string]$ImageFolderName = "",
	
	# Whether or not this build is also tagged as the latest
	[switch]$IsLatestTag = $true,
	
	# Additional versions for tagging the image
	# Can invoke as such: powershell.exe -Command "./test.ps1 -Name value1,value2,value3" 
	[Parameter(Mandatory=$false)]
	[string[]]$AdditionalTagVersions = @(),
	
	# Build arguments to supply when building the image 
	# So far only able to provide this parameter by invoking it this way (otherwise it sees it as a string):
	# powershell.exe -Command "./test.ps1 -Name @{SOME_KEY='thing'}" # PowerShell sucks
	[Parameter(Mandatory=$false)]
	[hashtable]$BuildArgs = @{},
	
	# Whether to include AWS keys as build arguments for making API calls (should try to avoid)
	[switch]$IncludeAwsKeysInBuildArgs = $false,
	
	# AWS Access Key for API calls 
	[Parameter(Mandatory=$true)]
	[string]$AwsAccessKey,
	
	# AWS Secret Key for API calls 
	[Parameter(Mandatory=$true)]
	[string]$AwsSecretKey,
	
	# AWS Session Token for API calls 
	[Parameter(Mandatory=$true)]
	[string]$AwsSessionToken,
	
	# Name of the AWS region where the repository is created 
	[Parameter(Mandatory=$false)]
	[string]$AwsRegion = "us-east-1",
	
	# Waits for user input before exiting, useful for testing builds to see output
	[switch]$Quiet = $false
)

Set-StrictMode -Version Latest

Try 
{
	# Find the repo path in solution
	$repoFolderName = $RepositoryName
	if ($ImageFolderName -ne "") {
		$repoFolderName = $ImageFolderName
	}
	
	# Verify the directory where the DockerFile should live exists
	# Warning: Relative path needs updated if ever moved elsewhere in solution
	$repoFolderPath = $PSScriptRoot + "\..\..\..\infrastructure\docker\repos\" + $repoFolderName
	if (-not (Test-Path $repoFolderPath)) {
		"Could not find repo path in solution" | Write-Host
		Exit 1
	}
	
	# Move CWD to the specific repo directory
	Push-Location $repoFolderPath

	# Setup our AWS session
	Set-AWSCredentials -AccessKey $AwsAccessKey -SecretKey $AwsSecretKey -SessionToken $AwsSessionToken
	Set-DefaultAWSRegion -Region $AwsRegion

	# Set environment variables for AWS CLI to be used instead of the credential file
	$env:AWS_ACCESS_KEY_ID = $AwsAccessKey
	$env:AWS_SECRET_ACCESS_KEY = $AwsSecretKey
	$env:AWS_SESSION_TOKEN = $AwsSessionToken
	$env:AWS_DEFAULT_REGION = $AwsRegion
	
	# Collect list of all tag versions to push to ECR 
	$tagVersions = @()
	$tagVersions += $AdditionalTagVersions
	if ($IsLatestTag) { $tagVersions += "latest" }
	$tagVersions = $tagVersions | sort -Unique
	
	if ($tagVersions.Count -eq 0) {
		"At least one tag version is needed to build" | Write-Host
		Exit 1
	}
	
	# Collect any build arguments to pass to Docker
	# If needed, AWS keys can be provided to Docker as build arguments (safe since build args arent available at runtime)
	if ($IncludeAwsKeysInBuildArgs) {
		$BuildArgs.Add("AWS_ACCESS_KEY_ID", $AwsAccessKey)
		$BuildArgs.Add("AWS_SECRET_ACCESS_KEY", $AwsSecretKey)
		$BuildArgs.Add("AWS_SESSION_TOKEN", $AwsSessionToken)
		$BuildArgs.Add("AWS_DEFAULT_REGION", $AwsRegion)
	}
	
	$buildArgsString = ""
	foreach ($key in $BuildArgs.Keys) {
		$buildArgsString = $buildArgsString + "--build-arg ${key}=$($BuildArgs.Item($key)) "
	}
	
	# Get our account number and let it fail if above credentials are incorrect
	$accountId = ""
	Try
	{
		"Fetching Account Id" | Write-Host
		$identity = Get-STSCallerIdentity
		$accountId = $identity.Account
	}
	Catch
	{
		"Incorrect AWS Credentials" | Write-Host
		Exit 1
	}
	
	# Create the repo if doesn't exist
	Try
	{
		"Ensuring repository exists" | Write-Host
		New-ECRRepository -RepositoryName $RepositoryName
	}
	Catch {}

	# Get docker login command for pushing to the repo
	$dockerLogin = aws ecr get-login --no-include-email --region $AwsRegion
	if ($LASTEXITCODE -ne 0) {
		"Failed fetching Docker login command for repository" | Write-Host
		Exit 1
	}	

	# Build the image and push as each provided tag to the repo
	$localTagName = "${RepositoryName}:build"
	$remoteTagBaseName = "${accountId}.dkr.ecr.${AwsRegion}.amazonaws.com/${RepositoryName}"
	Invoke-Expression -Command $dockerLogin
	Invoke-Expression "& docker build -t ${localTagName} ${buildArgsString} --no-cache ." | Out-Host
	
	foreach ($version in $tagVersions) {
		$remoteTagName = "${remoteTagBaseName}:${version}"
		Invoke-Expression "& docker tag ${localTagName} ${remoteTagName}" | Out-Host
		Invoke-Expression "& docker push ${remoteTagName}" | Out-Host
	}

	# Show results
	$pushedTagNames = $tagVersions -join ", "
	"Repository URI is ${remoteTagBaseName}" | Write-Host
	"Pushed the following tag versions: ${pushedTagNames}" | Write-Host
	
	# If quiet mode isn't set, wait for user to hit any key to exit
	if (-not($Quiet)) { 
		"Press any key to continue..." | Write-Host
		$pause = $host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown") 
	}
}
Finally 
{
	Pop-Location
}
