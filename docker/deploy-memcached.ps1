# Deploy Docker Image for Memcached 1.4.4 to AWS ECR Repository
# 
# Dependencies:
# <PERSON><PERSON>, AWS CLI, AWS Powershell Tools

param (
	[Parameter(Mandatory=$true)]
	[string]$AwsAccessKey,
	
	[Parameter(Mandatory=$true)]
	[string]$AwsSecretKey,
	
	[Parameter(Mandatory=$true)]
	[string]$AwsSessionToken,
	
	[switch]$IsProduction = $false,
	
	[switch]$Quiet = $false
)

# Strict Mode
Set-StrictMode -Version Latest

# Settings
$repositoryName = "memcached"
$repositoryFolderName = "memcached"
$specificVersionTag = "1.4.4" # in addition to 'latest'; for now, manually increment when we release a new image.

# For the provided account (assuming only ever have a non-prod and prod), create the Docker image in each region being used for that account.
# Essentially we're "hard-coding" the regions available per account since we don't have an easily accessible single source of truth for that yet.
if ($IsProduction) {
	Invoke-Expression "& powershell.exe -ExecutionPolicy Bypass -Command `"./utils/deploy-image.ps1 -RepositoryName $repositoryName -ImageFolderName $repositoryFolderName -AdditionalTagVersions $specificVersionTag -IsLatestTag:$false -AwsAccessKey $AwsAccessKey -AwsSecretKey $AwsSecretKey -AwsSessionToken $AwsSessionToken`"" | Out-Host

	Invoke-Expression "& powershell.exe -ExecutionPolicy Bypass -Command `"./utils/deploy-image.ps1 -RepositoryName $repositoryName -ImageFolderName $repositoryFolderName -AdditionalTagVersions $specificVersionTag -IsLatestTag:$false -AwsRegion us-west-2 -AwsAccessKey $AwsAccessKey -AwsSecretKey $AwsSecretKey -AwsSessionToken $AwsSessionToken`"" | Out-Host
} 
else {
	Invoke-Expression "& powershell.exe -ExecutionPolicy Bypass -Command `"./utils/deploy-image.ps1 -RepositoryName $repositoryName -ImageFolderName $repositoryFolderName -AdditionalTagVersions $specificVersionTag -IsLatestTag:$false -AwsAccessKey $AwsAccessKey -AwsSecretKey $AwsSecretKey -AwsSessionToken $AwsSessionToken`"" | Out-Host
}

# If quiet mode isn't set, wait for user to hit any key to exit
if (-not($Quiet)) { 
	"Press any key to continue..." | Write-Host
	$pause = $host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown") 
}
