#addin "Cake.FileHelpers&version=4.0.1"

#addin "nuget:?package=Newtonsoft.Json&version=11.0.2"
#addin "nuget:?package=AWSSDK.Core&version=3.3.24.3"
#addin "nuget:?package=AWSSDK.ECR&version=3.3.3.12"
#addin "nuget:http://proget.homenet.local/nuget/iss?package=Homenet.MoreCommon"
#addin nuget:?package=Cake.Docker&version=0.11.1

#load "utils/EcrRepository.cake"

using System.IO;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Homenet.MoreCommon;
using Homenet.MoreCommon.Amazon;
using Homenet.MoreCommon.SettingsHelpers;
using Amazon;
using Amazon.ECR;
using Amazon.ECR.Model;
using Amazon.Runtime;
using Amazon.RegionEndpoint;


// Environment Variables
string target = Argument("target", "Default");
string environment = Argument("environment", EnvironmentVariable("environment") ?? "scratch");
var hnEnvironment = Homenet.MoreCommon.Settings.GetEnvironment(environment.StartsWith("prod") ? "production-dd" : "development");

// Get the target tag version for the base processing app image
// ******
// This needs manually updated when upgrading to a new version of the base image that we've released. The reason for keeping 
// its default in code instead of the build server is so we're able to test base image changes in different branches. We're now 
// maintaining a tag version (should match default below) in the script that we use to manually deploy the base image so we always 
// deploy as a new specific version as well as latest. It could still be useful to change from whatever calls the Cake script so 
// allowing it as an arument for those who know how to use it. 
// ******
string targetBaseImageTagVersion = "1.1";
targetBaseImageTagVersion = Argument("targetBaseImageTagVersion", EnvironmentVariable("targetBaseImageTagVersion") ?? targetBaseImageTagVersion);

// Constants and Variables
const string rootPath = "../";
const string zipFileName = "codebasePackage.zip";
const string localBaseImageNameAndTag = "processingappbase:build"; // Matches task image DockerFile's FROM clause
string dockerFilePath = $"{rootPath}/infrastructure/docker/repos/processing_task/";
string packagePath = $"{dockerFilePath}{zipFileName}";
string baseImageNameAndTag = $"processing-base:{targetBaseImageTagVersion}"; 
string repositoryName = $"processingapps_{environment}";
string remoteImageName = null;

// AWS Variables
string defaultRegions = hnEnvironment.IsProduction ? "us-east-1,us-west-2" : "us-east-1";
string regions = Argument("regions", EnvironmentVariable("regions") ?? defaultRegions);
string[] regionsArray = regions.Split(new[]{','});
string account = Argument("account", "awsaaianp");
string profileName = Argument("profileName", $"{account}:PowerUser");
Dictionary<string,string> alksEnvVars;
SessionAWSCredentials sessionCredentials = null;
List<EcrRepositoryAndDockerCredentials> ecrRepositoryAndDockerCredentials = null;


/// 
/// Grab the session credentials from ALKS 
/// 
Task("Alks")
	.Does(()=>
	{
        Console.WriteLine($"Getting aws credentials for {profileName} from alks\n");
		
        var alksUserName = hnEnvironment.AlksAutoUpdate.ADUsername;
        var alksPassword = hnEnvironment.AlksAutoUpdate.ADPassword.Value;        
        var alks = new AirLiftKeyServices(alksUserName, alksPassword, "awsaaia*:PowerUser", profileName); 
        
		sessionCredentials = new SessionAWSCredentials(alks.DefaultProfile.Key.AccessKey, alks.DefaultProfile.Key.SecretKey, alks.DefaultProfile.Key.SessionToken);       		                
		alksEnvVars = alks.DefaultProfile.Key.AsEnvVars();
		ecrRepositoryAndDockerCredentials = EcrRepositoryAndDockerCredentials.GetEcrRepositoryAndDockerCredentialsForRegion(sessionCredentials, regionsArray, repositoryName);
	});

///
/// Zips up the content
///
Task("Package")
    .Does(() => {
		//clean up from previous run
		if (System.IO.File.Exists(packagePath)){
			System.IO.File.Delete(packagePath);			
		}

		// Zip up entire root folder
        Zip(rootPath, packagePath);
    });

///
/// Logs Docker in to the AWS repo
///
Task("LoginDocker")
	.IsDependentOn("Alks")
    .Does(() => {
		foreach (var item in ecrRepositoryAndDockerCredentials)
		{
			DockerLogin(item.DockerCredentials.Username, item.DockerCredentials.Password, item.DockerCredentials.HostName);
		}
    });
	
///
/// Pulls the base image from AWS and adds a local tag
///
Task("DockerPullAndTag")
	.IsDependentOn("LoginDocker")
    .Does(() => {
		//We only need to pull the remote image from one of the regions
		var dockerCredentials = ecrRepositoryAndDockerCredentials.First().DockerCredentials;
		remoteImageName = $"{dockerCredentials.HostName}/{baseImageNameAndTag}";
		DockerPull(remoteImageName);
		DockerTag(remoteImageName, localBaseImageNameAndTag);
    });

///
/// Builds the docker file
///
Task("BuildDockerFile")
	.IsDependentOn("DockerPullAndTag")
	.IsDependentOn("Package")
    .Does(() => {
		DockerBuild(new DockerImageBuildSettings{
			ForceRm = true,
			Tag = ecrRepositoryAndDockerCredentials.Select(e => $"{e.DockerCredentials.HostName}/{repositoryName}:latest").ToArray()
		}, dockerFilePath);

		//clean up from this run
		if (System.IO.File.Exists(packagePath)){
			System.IO.File.Delete(packagePath);			
		}
    });

/// 
/// Pushes docker to AWS
/// 
Task("PushDockerImage")
	.IsDependentOn("BuildDockerFile")
	.Does(()=>
    {
		foreach (var item in ecrRepositoryAndDockerCredentials)
		{
			DockerPush($"{item.DockerCredentials.HostName}/{repositoryName}:latest");
		}
    });
	
/// 
/// Cleans up local docker images that were created for this process
/// 
Task("RemoveDockerImages")
	.Does(()=>
    {
		//	This needs Alks to run before it can run, but it should have already run before we get here, and we don't necessarily want to run it again
		if (ecrRepositoryAndDockerCredentials == null){
			RunTarget("Alks");
		}
		List<string> imagesToBeRemoved = new List<string>();
		if (remoteImageName != null) imagesToBeRemoved.Add(remoteImageName);
		if (localBaseImageNameAndTag != null) imagesToBeRemoved.Add(localBaseImageNameAndTag);
		imagesToBeRemoved.AddRange(ecrRepositoryAndDockerCredentials.Select(e => $"{e.DockerCredentials.HostName}/{repositoryName}:latest"));
		DockerRemove(new DockerImageRemoveSettings{
				Force = true
			},
			imagesToBeRemoved.ToArray());
    })
	.OnError(exception =>
	{
		Console.WriteLine("RemoveDockerImages errored, Alks might not have run before this task");
		throw exception;
	});
	
/// 
/// Logout of aws docker servers
/// 
Task("LogoutDocker")
	.Does(()=>
    {
		if (ecrRepositoryAndDockerCredentials != null)
			foreach (var item in ecrRepositoryAndDockerCredentials)
			{
				DockerLogout(item.DockerCredentials.HostName);
			}
    })
	.OnError(exception =>
	{
		Console.WriteLine("LogoutDocker errored, Alks might not have run before this task");
		throw exception;
	});
	
/// 
/// Handles the full deployment and cleanup of docker images for processing app
/// 
Task("Deploy")
	.IsDependentOn("PushDockerImage")
	.IsDependentOn("RemoveDockerImages")
	.IsDependentOn("LogoutDocker")
	.Does(()=>
    {
		//Everything is handled in the dependant tasks		
    });

/// 
/// Removes s3 files from environment folder on s3
/// 
Task("Destroy")
	.IsDependentOn("Alks")
	.Does(()=>
	{
		foreach (var item in ecrRepositoryAndDockerCredentials)
		{
			item.EcrRepository.DeleteRepository();
		}
	});

/// 
/// Default task is run if no task is specified
/// 
Task("Default")
	.IsDependentOn("Package")
	.Does(() => {})
	.OnError(exception =>
	{
		Console.WriteLine("Default errored");
		throw exception;
	});


// Run the target
RunTarget(target);