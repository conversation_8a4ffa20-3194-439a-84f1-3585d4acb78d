#addin "nuget:?package=Newtonsoft.Json&version=11.0.2"
#addin "nuget:?package=AWSSDK.KeyManagementService&version=3.7.0.30"
#addin "nuget:?package=AWSSDK.Core&version=3.7.1"
#addin "nuget:?package=AWSSDK.RDS&version=3.3.24.1"
#addin "nuget:http://proget/nuget/iss?package=Homenet.MoreCommon"

#load "utils/TerraformArgs.cake"
#load "utils/TerraformComponentConfig.cake"

using System.IO;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Homenet.MoreCommon;
using Homenet.MoreCommon.Amazon;
using Homenet.MoreCommon.SettingsHelpers;
using Amazon.KeyManagementService;
using Amazon.KeyManagementService.Model;
using Amazon.Runtime;
using Amazon.RDS;
using Amazon.RDS.Model;


// Definitions
const string ApplicationAbbreviation = "ais10";
const string TerraformVersion = "0.13.7";
const string DefaultRegionName = "us-east-1";
const string ZipPath = "./zip/";
const string ArtifactPath = "../artifacts";
const string LambdaArtifactPath = TerraformComponentConfig.LambdaArtifactFolderPath;
const string LambdaSourcePath = TerraformComponentConfig.LambdaSourceFolderPath;
const string LambdaNpmInstallBatPath = "./npm.bat";
const string MySQLVersionSuffix = "57";

// Declarations
Dictionary<string,string> alksEnvVars;
Dictionary<string,string> terraformArguments;
SessionAWSCredentials sessionCredentials = null;
string terraformExePath = string.Empty;

// Exit code handling
int lastExitCode = 0;
Action<int> VerifyExitCode = (int exitCode) => {
	if (exitCode != 0) throw new Exception($"Task exited with an exit code of {exitCode}");
};

// Function to read Cake variable from arguments or environment variable or the packages dictionary
Func<string,string,string> GetCakeArgument = (string argumentName, string defaultValue) => {
	return Argument(argumentName, EnvironmentVariable(argumentName) ?? defaultValue);
};

// Function to return an abbreviated identifier for an AWS region which we use to name the region-specific shared variable files
Func<string,string> GetAbbreviationForRegion = (string regionName) => {
	string[] segments = regionName.Split('-');
	return string.Concat(segments[0][0], segments[1][0], segments[2]); // us-east-1 => ue1
};

// AWS Arguments
var target = Argument("target", "Default");
var account = Argument("account", "awsaaianp");
var onCluster = HasArgument("on-cluster") ? Argument<string>("on-cluster") : null;
var profileName = Argument("profileName", $"{account}:PowerUser");
var regionName = Argument("region", DefaultRegionName);

// Build Arguments
var environment = GetCakeArgument("environment", string.Empty);
var component = GetCakeArgument("component", string.Empty);
var buildNumber = Argument("buildNumber", EnvironmentVariable("BUILD_NUMBER") ?? DateTime.Now.ToString("yyyyMMddHHmmss"));
var launchedBy = Argument("launchedBy", EnvironmentVariable("USERNAME"));
var launchedOn = Argument("launchedOn", DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ssZ"));

// Determine base environment
var baseEnvironment = environment.StartsWith("scratch") ? "scratch"
					: environment.StartsWith("uat") ? "uat"
					: environment.StartsWith("qamain") ? "qamain"
					: environment.StartsWith("qarapid") ? "qarapid"
					: environment.StartsWith("homenet") ? "homenet"
					: environment.StartsWith("production") ? "production"
					: environment.StartsWith("nonprod") ? "nonprod"
					: environment;

// Misc
var componentConfig = new TerraformComponentConfig(baseEnvironment, component, GetCakeArgument);
var hnEnvironment = Homenet.MoreCommon.Settings.GetEnvironment(baseEnvironment == "production" ? "production-dd" : "development");
string accountType = hnEnvironment.IsProduction ? "prod" : "nonprod";
string regionAbbreviation = GetAbbreviationForRegion(regionName);
var tfEnvironmentDirectory = new DirectoryPath($"../infrastructure/environments/{baseEnvironment}/{component}/");
var sharedVariableAccountFile = new FilePath($"../infrastructure/shared-variables.{accountType}.tf");
var sharedVariableRegionFile = new FilePath($"../infrastructure/shared-variables.{accountType}.{regionAbbreviation}.tf");


/// 
/// Verify safety checks to ensure we have all the inputs needed to build
/// 
Task("SafetyCheck")
	.Does(()=>
	{
        Console.WriteLine($"Performing safety checks");
		
		// Verify arguments that are required but don't have any defaults available (via hard-coded nor environment variable)
        if (string.IsNullOrEmpty(environment))
			throw new Exception("Must specify an environment name to be deployed with Terraform");
        if (string.IsNullOrEmpty(component))
			throw new Exception("Must specify a component name for the environment to be deployed with Terraform");
        if (string.IsNullOrEmpty(regionName))
			throw new Exception("Must specify a region name for the environment to be deployed with Terraform");
		
		// Verify the region is a real thing
		if (Amazon.RegionEndpoint.GetBySystemName(regionName).ToString().StartsWith("Unknown"))
			throw new Exception($"Invalid region specified as {regionName}");
		
		// Verify a valid environment and component combination to be deployed
		if (!DirectoryExists(tfEnvironmentDirectory))
			throw new Exception($"Invalid combination of environment and component: can't find path {tfEnvironmentDirectory.FullPath}");
		
		// Verify the shared variable files that are needed exist
		if (!FileExists(sharedVariableAccountFile))
			throw new Exception($"Unable to find shared variable file for account at {sharedVariableAccountFile.FullPath}");
		if (!FileExists(sharedVariableRegionFile))
			throw new Exception($"Unable to find shared variable file for region at {sharedVariableRegionFile.FullPath}");
		
		// After verifying the environment/component, load the associated component config json (which depends on those values) 
		// and then verify the config parameters for the component.
		componentConfig.TryLoadConfigFile();
		componentConfig.ValidateConfigParameters();
	});

///
/// If the Terraform component's config JSON has any special lambda type parameters, package them 
/// into zips to push to AWS. When parameter values are requested from the `componentConfig` to provided
/// to Terraform, instead of looking for command-line arguments, it automatically provides the path to 
/// the associated zip file being packaged here.
///
Task("PackageLambdas")
    .IsDependentOn("SafetyCheck")
    .IsDependentOn("NpmVersion")
    .Does(() => {
		// Ensure the output subfolder for settings (under the artifact directory) exists in a clean state
		if (!DirectoryExists(ArtifactPath)) CreateDirectory(ArtifactPath);
		if (DirectoryExists(LambdaArtifactPath)) DeleteDirectory(LambdaArtifactPath, new DeleteDirectorySettings { Recursive = true, Force = true });
		CreateDirectory(LambdaArtifactPath);

		// Loop through any lambda parameters from the config and zip each item
		var lambdaFolderNames = componentConfig.GetLambdaFolderNamesNeedingPackaged();
		foreach (string sourceFolder in lambdaFolderNames)
		{
			Console.WriteLine($"Packaging lambda {sourceFolder}");

			// Run NPM install if there's a package.json file since we need to publish the modules as well
			if (FileExists(System.IO.Path.Combine(LambdaSourcePath, sourceFolder, "package.json")))
			{
				Console.WriteLine($"Found package.json so running NPM install for {sourceFolder}");

				lastExitCode = StartProcess(System.IO.Path.GetFullPath(LambdaNpmInstallBatPath), new ProcessSettings {
					WorkingDirectory = System.IO.Path.Combine(LambdaSourcePath, sourceFolder)
				});

				VerifyExitCode(lastExitCode);
			}

			Zip(System.IO.Path.Combine(LambdaSourcePath, sourceFolder), System.IO.Path.Combine(LambdaArtifactPath, sourceFolder + ".zip"));
		}
    });
	
/// 
/// Ensure that we have the correct version of NPM
/// 
Task("NpmVersion")
	.Does(()=>
    {
        Console.WriteLine("Verifying NPM installation for correct version with Chocolatey");
		
		// Will install latest version if not already installed, otherwise will skip since you'd need to use a force/reinstall or upgrade flag
		lastExitCode = StartProcess("choco", new ProcessSettings { Arguments = $"install -y nodejs.install" });
		VerifyExitCode(lastExitCode);
    });

/// 
/// Grab the session credentials from ALKS so we can perform terraform deployments
/// 
Task("Alks")
	.Does(()=>
	{
        Console.WriteLine($"Getting AWS Credentials for {profileName} from ALKS");
		
        var alks = new AirLiftKeyServices(hnEnvironment.AlksAutoUpdate.ADUsername, hnEnvironment.AlksAutoUpdate.ADPassword.Value, "awsaaia*:PowerUser", profileName);				        
		alksEnvVars = alks.DefaultProfile.Key.AsEnvVars();
		
		sessionCredentials = new SessionAWSCredentials(alks.DefaultProfile.Key.AccessKey, alks.DefaultProfile.Key.SecretKey, alks.DefaultProfile.Key.SessionToken); 
	});

/// 
/// Ensure that we have the correct version of terraform
/// 
Task("TerraformVersion")
	.Does(()=>
    {
        Console.WriteLine("Verifying Terraform installation for correct version with Chocolatey");
		
		string chocoPath = System.Environment.GetEnvironmentVariable("ChocolateyInstall");
		terraformExePath = System.IO.Path.Combine(chocoPath, "lib", $"terraform", "tools");
		
		if (!System.IO.Directory.Exists(terraformExePath))
		{
			// Must specify multi-version flag as well as the force flag to make sure it'll reinstall with a versioned folder name if already installed
			lastExitCode = StartProcess("choco", new ProcessSettings {
				Arguments = $"install terraform --version {TerraformVersion} -my --force",
			});
			
			VerifyExitCode(lastExitCode);
		}
    });
	
/// 
/// Build terraform arguments for the build
/// 
Task("TerraformArgs")
    .Does(() => 
    {        
        Console.WriteLine("Setting terraform arguments");
		
		// Global arguments for components (i.e. for shared-variables.global.tf)
        terraformArguments = new TerraformArgs 
        {
            { "region", regionName },
            { "build_number", buildNumber },
            { "environment", environment },
            { "launched_by", launchedBy },
            { "launched_on", launchedOn },
			{ "component", component },
            { "slack_contact", hnEnvironment.AISAuthoring.AWSNotificationSlackChannel.Replace("#", "+") } // use valid tags for monkey
        };

         if (!string.IsNullOrEmpty(onCluster))
                {
                    terraformArguments.Add("onCluster", onCluster);
                }
		
		// Custom arguments for components (i.e. for individual components' variables.tf)
		var componentArguments = componentConfig.GetTerraformArguments();
		foreach (var argument in componentArguments)
		{
			terraformArguments.Add(argument.Key, argument.Value);
			Console.WriteLine($"Config JSON Variable: {{ Key: {argument.Key}, Value: {argument.Value} }}");
		}
    });

/// 
/// Initialize the deployment (merge shared vars, download modules, and setup s3 state)
/// 
Task("InitDeployment")
    .IsDependentOn("SafetyCheck")
    .IsDependentOn("PackageLambdas")
    .IsDependentOn("TerraformVersion")
	.IsDependentOn("Alks")
    .IsDependentOn("TerraformArgs")
	.Does(()=>
    {
		var sharedVariableGlobalFile = new FilePath("../infrastructure/shared-variables.global.tf");
		var dataSourceGlobalFile = new FilePath("../infrastructure/data-sources.global.tf");
		string tfstateBucketKey = $"AIS-1.0/{environment}/{component}";
		string tfstateBucketName = $"ais.{accountType}.{regionAbbreviation}.infrastructure.tf.state";
		
		// Copy global shared variable tf file into the environment's component root module
		CopyFile(sharedVariableGlobalFile.FullPath, System.IO.Path.Combine(tfEnvironmentDirectory.FullPath, "shared-variables-global.tf"));

		// Copy the data sources tf file into the environment's component root module 
		CopyFile(dataSourceGlobalFile.FullPath, System.IO.Path.Combine(tfEnvironmentDirectory.FullPath, "shared-data-sources.tf"));
		
		// Copy account-specific shared variable tf file into the environment's component root module
		CopyFile(sharedVariableAccountFile.FullPath, System.IO.Path.Combine(tfEnvironmentDirectory.FullPath, "shared-variables-account.tf"));

		// Copy the region-specific shared variable tf file for the account into the environment's component root module 
		CopyFile(sharedVariableRegionFile.FullPath, System.IO.Path.Combine(tfEnvironmentDirectory.FullPath, "shared-variables-region.tf"));
		
		// Display version so we have record of it in the build logs just in case
		lastExitCode = StartProcess(System.IO.Path.Combine(terraformExePath, "terraform.exe"), new ProcessSettings { Arguments = "--version" });
		VerifyExitCode(lastExitCode);
		
		// Initialize terraform
		lastExitCode = StartProcess(System.IO.Path.Combine(terraformExePath, "terraform.exe"), new ProcessSettings {
            Arguments = $"init -input=false -get=true -backend-config=\"region={regionName}\" -backend-config=\"bucket={tfstateBucketName}\" -backend-config=\"key={tfstateBucketKey}\"",
            EnvironmentVariables = alksEnvVars,
            WorkingDirectory = tfEnvironmentDirectory
        });
		VerifyExitCode(lastExitCode);
    });

/// 
/// Run terraform plan to ensure the environment declaration is sane and creates a legit plan to apply
/// 
Task("Plan")
	.IsDependentOn("InitDeployment")
	.Does(()=>
    {		         
        lastExitCode = StartProcess(System.IO.Path.Combine(terraformExePath, "terraform.exe"), new ProcessSettings {
            Arguments = $"plan {terraformArguments} -out=plan",
            EnvironmentVariables = alksEnvVars,
            WorkingDirectory = tfEnvironmentDirectory
        });   
		
		VerifyExitCode(lastExitCode);
    });

/// 
/// Apply the plan (i.e. build the real AWS infrastructure)
/// 
Task("Apply")
    .IsDependentOn("Plan")
	.Does(()=>
    {
		// Apply the generated plan to sync cloud resources
        lastExitCode = StartProcess(System.IO.Path.Combine(terraformExePath, "terraform.exe"), new ProcessSettings {
            Arguments = "apply plan",
            EnvironmentVariables = alksEnvVars,
            WorkingDirectory = tfEnvironmentDirectory
        });   
		VerifyExitCode(lastExitCode);
    });

/// 
/// Destroy the AWS infrastructure
/// 
Task("Destroy")
	.IsDependentOn("InitDeployment")
	.Does(()=>
    {		         
        lastExitCode = StartProcess(System.IO.Path.Combine(terraformExePath, "terraform.exe"), new ProcessSettings {
            Arguments = $"destroy -auto-approve {terraformArguments}",
            EnvironmentVariables = alksEnvVars,
            WorkingDirectory = tfEnvironmentDirectory
        }); 
		
		VerifyExitCode(lastExitCode);
    });
	
/// 
/// Default task is run if no task is specified
/// 
Task("Default")
	.IsDependentOn("Plan")
	.Does(() => {})
	.OnError(exception =>
	{
		Console.WriteLine("Default errored");
		throw exception;
	});


// Run the provided target
RunTarget(target);
