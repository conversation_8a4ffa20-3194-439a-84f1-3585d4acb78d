# Deploy AMIs to AWS
# 
# Dependencies:
# AWS CLI, AWS Powershell Tools

param (
	[Parameter(Mandatory=$false)]
	[string]$Action = "Apply", # or "Plan" for testing, matching Cake task names
	
	[Parameter(Mandatory=$true)]
	[string]$AmiUserDataFolderName,
	
	[switch]$IsProduction = $false,
	
	[switch]$Quiet = $false
)

# Strict Mode
Set-StrictMode -Version Latest

# Account Settings
# -> Prod
$accountProduction = "awsaaia"
$environmentProduction = "production" # must start with this in Cake
# -> NonProd
$accountNonProduction = "awsaaianp"
$environmentNonProduction = "non-prod"

# Region Settings
# 
# <#Ais10BaseAmiDefs#>
# If moved or changed, remember to update the documentattion for creating and patching the ais10 webstack
# TODO:
# Should have param for this script to accept a dictionary of region => amiId so that this script can 
# be generic again to avoid having to copy-pasta per managed AMI that future us need.
# 
# aws ec2 describe-images --owners ************  --query 'sort_by(Images, &CreationDate)[*].[ImageId,Name,Description,ImageLocation,CreationDate]' --filters Name=description,Values="*Linux AMI*x86_64*gp2*"  --region us-east-1 --output table  | sort -k3
# -> UE1
$baseAmiIdUe1 = "ami-01a73f51321ab6899"
# aws ec2 describe-images --owners ************  --query 'sort_by(Images, &CreationDate)[*].[ImageId,Name,Description,ImageLocation,CreationDate]' --filters Name=description,Values="*Linux AMI*x86_64*gp2*"  --region us-west-2 --output table  | sort -k3
# -> UW2
$baseAmiIdUw2 = "ami-073fc203d1afbc646"

# For the provided account (assuming only ever have a non-prod and prod), create the AMI in each region being used for that account.
# Essentially we're "hard-coding" the regions available per account since we don't have an easily accessible single source of truth for that yet.
if ($IsProduction) {
	& ..\cake.ps1 -Script deployAmi.cake -experimental -Target $Action --account=$accountProduction --region=us-east-1 --environment=$environmentProduction --amiBaseImageId=$baseAmiIdUe1 --amiUserDataFolderName=$AmiUserDataFolderName
	
	& ..\cake.ps1 -Script deployAmi.cake -experimental -Target $Action --account=$accountProduction --region=us-west-2 --environment=$environmentProduction --amiBaseImageId=$baseAmiIdUw2 --amiUserDataFolderName=$AmiUserDataFolderName
} 
else {
	& ..\cake.ps1 -Script deployAmi.cake -experimental -Target $Action --account=$accountNonProduction --region=us-east-1 --environment=$environmentNonProduction --amiBaseImageId=$baseAmiIdUe1 --amiUserDataFolderName=$AmiUserDataFolderName
}

# If quiet mode isn't set, wait for user to hit any key to exit
if (-not($Quiet)) { 
	"Press any key to continue..." | Write-Host
	$pause = $host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown") 
}
