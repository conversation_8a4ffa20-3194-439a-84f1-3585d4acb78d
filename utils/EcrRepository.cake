///<summary>
/// Utility class to create/destroy repos, and get authentication info for docker login
///</summary>
public class EcrRepository 
{
    private readonly IAmazonECR _client;
    private readonly string _repositoryName;

    private static Dictionary<string, EcrRepository> EcrRepositoryByRegion = new Dictionary<string, EcrRepository>();
    private static Dictionary<string, DockerCredentials> DockerCredentialsByRegion = new Dictionary<string, DockerCredentials>();
    
    #region Constructors
    public EcrRepository(AWSCredentials sessionCredentials, string regionEndpointString, string repositoryName) 
    {
        RegionEndpoint regionEndpoint = RegionEndpoint.EnumerableAllRegions.FirstOrDefault(x => x.SystemName.Equals(regionEndpointString, StringComparison.InvariantCultureIgnoreCase)) ?? RegionEndpoint.USEast1;
        _client = new AmazonECRClient(sessionCredentials, regionEndpoint);        
        _repositoryName = repositoryName;
    }

    public EcrRepository(AWSCredentials sessionCredentials, RegionEndpoint regionEndpoint, string repositoryName) 
    {
        _client = new AmazonECRClient(sessionCredentials, regionEndpoint);        
        _repositoryName = repositoryName;
    }
    #endregion

    #region Public Methods    
    //Helper method to create a repo if it doesn't exist, then get docker authorization info for repo
    public DockerCredentials GetAuthorizationDataForRepo()
    {
        var repository = GetRepository(_client, _repositoryName) ?? CreateRepository(_client, _repositoryName);
        var authorizationData = GetAuthorizationDataForRepo(_client, repository);
		
        var tokenSegments = System.Text.Encoding.ASCII.GetString(Convert.FromBase64String(authorizationData.AuthorizationToken)).Split(':');
        var dockerCredentials = new DockerCredentials {
            HostName = new Uri(authorizationData.ProxyEndpoint).DnsSafeHost,
            Username = tokenSegments[0],
            Password = tokenSegments[1]
        };
        return dockerCredentials;
    }

    //Deletes a repository (if it exists)
    public void DeleteRepository()
    {
        var repo = GetRepository(_client, _repositoryName);
        if (repo != null)
            _client.DeleteRepositoryAsync(new DeleteRepositoryRequest
                {
                    Force = true,
                    RegistryId = repo.RegistryId,
                    RepositoryName = repo.RepositoryName
                }).Wait();
    }
    #endregion

    #region Private Methods
    private Repository GetRepository(IAmazonECR client, string repoName)
    {
        string nextToken = string.Empty;
        bool getNext = true;
        var repos = new List<Repository>();
        while (getNext)
        {
            var request = new DescribeRepositoriesRequest
                {
                    MaxResults = 100,
                };
            if (!string.IsNullOrWhiteSpace(nextToken))
                request.NextToken = nextToken;

            var result = client.DescribeRepositoriesAsync(request).Result;
            repos.AddRange(result.Repositories);
            nextToken = result.NextToken;
            if (string.IsNullOrWhiteSpace(nextToken))
                getNext = false;
        }
        
        return repos.SingleOrDefault(r =>
            r.RepositoryName.Equals(repoName, StringComparison.InvariantCultureIgnoreCase));
    }

    private Repository CreateRepository(IAmazonECR client, string repoName)
    {
        var request = new CreateRepositoryRequest
            {
                RepositoryName = repoName
            };

        var result = client.CreateRepositoryAsync(request).Result;
        //Add the policy that allows tasks to access it from this account
        AddRepositoryPolicy(client, result.Repository);
        return result.Repository;
    }
    
    private AuthorizationData GetAuthorizationDataForRepo(IAmazonECR client, Repository repository)
    {
        return client.GetAuthorizationTokenAsync(new GetAuthorizationTokenRequest
            {
                RegistryIds = new List<string>
                    {
                        repository.RegistryId
                    }
            }).Result.AuthorizationData.First();
    }

    private void AddRepositoryPolicy(IAmazonECR client, Repository repository)
    {
        client.SetRepositoryPolicyAsync(new SetRepositoryPolicyRequest
            {
                RegistryId = repository.RegistryId,
                RepositoryName = repository.RepositoryName,
                PolicyText = 
                    $"{{\n  \"Version\" : \"2008-10-17\",\n  \"Statement\" : [ {{\n    \"Sid\" : \"AWS Account Level\",\n    \"Effect\" : \"Allow\",\n    \"Principal\" : {{\n      \"AWS\" : \"arn:aws:iam::{repository.RegistryId}:root\"\n    }},\n    \"Action\" : [ \"ecr:GetDownloadUrlForLayer\", \"ecr:BatchGetImage\", \"ecr:BatchCheckLayerAvailability\" ]\n  }} ]\n}}"
            }).Wait();
    }
    #endregion
}

public class DockerCredentials {
    public string HostName {get; set;}
    public string Username {get; set;}
    public string Password {get; set;}
}

public class EcrRepositoryAndDockerCredentials {
    public DockerCredentials DockerCredentials {get; set;}
    public EcrRepository EcrRepository {get; set;}
    
    public static List<EcrRepositoryAndDockerCredentials> GetEcrRepositoryAndDockerCredentialsForRegion(AWSCredentials sessionCredentials, string[] regionEndpointStrings, string repositoryName){
        var repoAndCredsList = new List<EcrRepositoryAndDockerCredentials>();
        foreach (var regionEndpointString in regionEndpointStrings)
        {
            var ecrRepository = new EcrRepository(sessionCredentials, regionEndpointString, repositoryName);
            repoAndCredsList.Add(new EcrRepositoryAndDockerCredentials{
                EcrRepository = ecrRepository,
                DockerCredentials = ecrRepository.GetAuthorizationDataForRepo()
            });
        }
        return repoAndCredsList;        
    }
}