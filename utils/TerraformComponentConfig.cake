using System;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using Newtonsoft.Json;

#load "TerraformArgs.cake"

///<summary>
/// Configuration wrapper for a deployable component for an environment
/// 
/// Notes: 
/// This class assumes that the build script is run from /build for relative paths to work correctly.
///</summary>
public class TerraformComponentConfig
{
    private const string ComponentConfigFolderPath = "../infrastructure/configs/components";
    public const string LambdaSourceFolderPath = "../infrastructure/lambda";
    public const string LambdaArtifactFolderPath = "../artifacts/lambda";
	
	private bool _isConfigLoaded;
	private IEnumerable<ConfigItem> _configParameters;
	private Func<string,string,string> _getCakeArgument;
	
	public string BaseEnvironmentName { get; }
	public string ComponentName { get; }
	public string ComponentFolderPath => $"../infrastructure/environments/{BaseEnvironmentName}/{ComponentName}";
	public bool HasParameters => _configParameters != null && _configParameters.Any();
	
	public TerraformComponentConfig(string baseEnvironmentName, string componentName, Func<string,string,string> getCakeArgument)
	{
		// Need this Func<> due to issues referencing Cake contexts to call methods for arguments and environment variables
		_getCakeArgument = getCakeArgument;
		
		BaseEnvironmentName = baseEnvironmentName;
		ComponentName = componentName;
	}
	
	public void TryLoadConfigFile()
	{
		var prioritizedConfigFiles = new List<string> 
		{
			$"{ComponentFolderPath}/config.json",                // most specific
			$"{ComponentConfigFolderPath}/{ComponentName}.json", // least specific
		};
		
		// Note that we attempted loading the config
		_isConfigLoaded = true;
		
		// Find the most specific config file that exists in the solution
		string targetConfigFile = null;
		foreach (string configFile in prioritizedConfigFiles)
		{
			if (System.IO.File.Exists(configFile)) 
			{
				targetConfigFile = configFile;
				break;
			}
		}
		
		// All done if we have no config file (could very well be intentional if the component only depends on shared global var file)
		if (targetConfigFile == null) return;
		
		// Ensure the file is valid JSON or throw a tantrum so someone can fix it
		string configFileContents = System.IO.File.ReadAllText(targetConfigFile);
		if (!IsValidJson(configFileContents))
			throw new Exception($"Invalid JSON for component file at {targetConfigFile} for deploying {ComponentName} to {BaseEnvironmentName}");
		
		// Deserialize and store our config for this deployment
		_configParameters = JsonConvert.DeserializeObject<List<ConfigItem>>(configFileContents);
	}
		
	public void ValidateConfigParameters()
	{
		// Ensure config file has been explicitly loaded
		VerifyConfigFileLoaded();
		
		// Cant validate if we have no parameters from the json
		if (!HasParameters) return;
		
		// Test each parameter against its validation rule and build a list of any failings
		var validationErrors = new List<string>();
		foreach (ConfigItem parameter in _configParameters)
		{
			// For the special lambda parameter type, validate that the source directory exists as expected
			if (!string.IsNullOrEmpty(parameter.LambdaFolderName)) 
			{
				string lambdaFolderPath = System.IO.Path.GetFullPath(System.IO.Path.Combine(LambdaSourceFolderPath, parameter.LambdaFolderName));
				if (!System.IO.Directory.Exists(lambdaFolderPath))
					validationErrors.Add($"Component {ComponentName} failed validation due to missing Lambda source for {parameter.TerraformVariableName} at {lambdaFolderPath}");
				
				continue;
			}
			
			// Ignore validation if no regex was provided
			if (string.IsNullOrEmpty(parameter.ValidationRegex)) 
				continue;
			
			// Get the parameter value from Cake and check if we should continue to validate
			string paramValue = GetCakeArgument(parameter);
			if (string.IsNullOrEmpty(paramValue) && parameter.IsOptional) 
				continue;
			
			// Validate the parameter's value against the provided regex
			var validRegex = new System.Text.RegularExpressions.Regex(parameter.ValidationRegex);
			if (!validRegex.IsMatch(paramValue))
				validationErrors.Add($"Component {ComponentName} failed validation for argument {parameter.CakeArgumentName}: {parameter.ValidationFailedText}");
		}
		
		// If validation errors exist, throw all of them as one message for Cake logs
		if (validationErrors.Any()) 
			throw new Exception("Can't get Terraform arguments due to validation errors that need resolved:\n" + string.Join("\n", validationErrors));
	}
	
	public TerraformArgs GetTerraformArguments()
	{
		ValidateConfigParameters();
		
		// Return empty argument set if no parameters from the json
		var arguments = new TerraformArgs();
		if (!HasParameters) 
			return arguments;
		
		// Build the arguments by reading from command-line arguments with a fallback to environment variables
		foreach (ConfigItem parameter in _configParameters)
		{
			string paramValue = GetCakeArgument(parameter);
			if (string.IsNullOrEmpty(paramValue) && parameter.IsOptional) 
				continue;
			
			arguments.Add(parameter.TerraformVariableName, paramValue);
		}
		
		return arguments;
	}

	public List<string> GetLambdaFolderNamesNeedingPackaged()
	{
		ValidateConfigParameters();
		
		// Return empty argument set if no parameters from the json
		if (!HasParameters) return new List<string>();
		
		return _configParameters.Where(p => !string.IsNullOrEmpty(p.LambdaFolderName)).Select(p => p.LambdaFolderName).ToList();
	}
	
	#region Private Methods
	
	private string GetCakeArgument(ConfigItem parameter)
	{
		// Handle special parameters for lambdas by returning the path of the packaged zip file
		if (!string.IsNullOrEmpty(parameter.LambdaFolderName)) 
		{
			return System.IO.Path.GetFullPath(System.IO.Path.Combine(LambdaArtifactFolderPath, parameter.LambdaFolderName + ".zip"));
		}
		
		return _getCakeArgument(parameter.CakeArgumentName, parameter.CakeArgumentDefaultValue ?? string.Empty);
	}
	
	private bool IsValidJson(string allegedJson)
	{
		allegedJson = allegedJson.Trim();
		
		if ((allegedJson.StartsWith("{") && allegedJson.EndsWith("}")) || (allegedJson.StartsWith("[") && allegedJson.EndsWith("]")))
		{
			try
			{
				var json = JToken.Parse(allegedJson);
				return true;
			}
			catch (Exception) {}
		}
		
		return false;
	}
	
	private void VerifyConfigFileLoaded()
	{
		if (!_isConfigLoaded)
			throw new Exception("Must call TryLoadConfigFile method before trying to validate and/or get argument values");
	}
	
	#endregion
	
	#region Private Classes
	
	/// <summary>
	/// Model for an item in the config json that represents a parameter being provided to Terraform
	/// </summary>
	private class ConfigItem
	{
		/// <summary>
		/// Name of the Terraform variable (var.name) that accepts the value.
		/// </summary>
		public string TerraformVariableName { get; set; }
		
		/// <summary>
		/// Name of the Cake variable to read the raw value from the build script invocation.
		/// 
		/// Checks for the value as an argument (--name=value) and falls back to checking for an environment variable with same name.
		/// If the value isn't found using both methods then it will use `CakeArgumentDefaultValue` as the value if provided.
		/// </summary>
		public string CakeArgumentName { get; set; }
		
		/// <summary>
		/// Default value for `CakeArgumentName` to be used when no matching argument or environment variable is found.
		/// 
		/// When a default value is provided, it will still be validated like any other value passed in so it doens't 
		/// get a free ride into Terraform's invocation.
		/// </summary>
		public string CakeArgumentDefaultValue { get; set; }
		
		/// <summary>
		/// Whether or not the parameter is optional.
		/// 
		/// Optional parameters aren't subjected to validation and are omitted from Terraform's invocation, but only   
		/// if fetching its value results in an empty string (after checking env vars, cake args, and default value). 
		/// </summary>
		public bool IsOptional { get; set; }
		
		#region Validation
				
		/// <summary>
		/// Validation regular expression to test against the value before passing it to Terraform.
		/// </summary>
		public string ValidationRegex { get; set; }
		
		/// <summary>
		/// Validation message to display when a value doesn't pass against `ValidationRegex`.
		/// </summary>
		public string ValidationFailedText { get; set; } = "N/A";
		
		#endregion
		
		#region Special Types
		
		/// <summary>
		/// Name of the sub-directory (under the lambda base directory path)
		/// 
		/// Special parameter type that automatically causes the associated lambda to be packaged and its zip path to be provided as the value for Terraform. 
		/// When specifying this for lambda parameter, the only other config property needed is `TerraformVariableName` since the rest are irrelevant for this case.
		/// </summary>
		public string LambdaFolderName { get; set; }
		
		#endregion
	}
	
	#endregion
}