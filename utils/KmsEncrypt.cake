///<summary>
/// Utility class to encrypt string values using AWS KMS provided a KMS Key arn and a set of credentials
///</summary>
public class KmsEncrypt 
{
    private readonly AmazonKeyManagementServiceClient _client;
    private readonly RegionEndpoint _region;
    private readonly string _kmsKeyArn;
    
    public KmsEncrypt(SessionAWSCredentials sessionCredentials, RegionEndpoint region, string kmsKeyArn) 
    {
        _client = new AmazonKeyManagementServiceClient(sessionCredentials, region);        
        _kmsKeyArn = kmsKeyArn;
    }
    
    //Helper method to encrypt the provided string given a KMS client and a KMS Key ARN
    public string Encrypt(string value) {        
        var request = new EncryptRequest
        {
            KeyId = _kmsKeyArn,
            Plaintext = new MemoryStream(Encoding.Default.GetBytes(value))
        };

        var result = _client.EncryptAsync(request, System.Threading.CancellationToken.None).Result;    
        return Convert.ToBase64String(result.CiphertextBlob.ToArray());        
    }
}

